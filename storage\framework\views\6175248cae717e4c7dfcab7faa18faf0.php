<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps([
    'startDate' => null,
    'endDate' => null,
    'placeholder' => 'Pilih rentang tanggal',
    'id' => 'modern-daterange-picker-' . uniqid()
]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps([
    'startDate' => null,
    'endDate' => null,
    'placeholder' => 'Pilih rentang tanggal',
    'id' => 'modern-daterange-picker-' . uniqid()
]); ?>
<?php foreach (array_filter(([
    'startDate' => null,
    'endDate' => null,
    'placeholder' => 'Pilih rentang tanggal',
    'id' => 'modern-daterange-picker-' . uniqid()
]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<div
    x-data="modernDateRangePicker({
        startDate: '<?php echo e($startDate); ?>',
        endDate: '<?php echo e($endDate); ?>',
        id: '<?php echo e($id); ?>'
    })"
    x-init="init()"
    x-destroy="destroy()"
    class=" w-full modern-date-range-picker"
    @click.outside="closeDropdown()"
>
    
    <div class="">
        <button
            x-ref="toggleButton"
            @click="toggleDropdown()"
            type="button"
            class="flex items-center justify-between w-full px-4 py-3 text-left transition-all duration-300 bg-white border-2 border-gray-200 shadow-sm dark:bg-slate-800 dark:border-slate-600 rounded-xl hover:border-yellow-400 dark:hover:border-yellow-400 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-yellow-500 dark:focus:ring-yellow-400 focus:border-yellow-500 dark:focus:border-yellow-400 group"
        >
            <div class="flex items-center">
                
                <div class="flex items-center justify-center w-10 h-10 mr-3 transition-all duration-300 bg-gray-100 rounded-lg dark:bg-slate-700 group-hover:bg-yellow-100 dark:group-hover:bg-yellow-400/20">
                    <svg class="w-5 h-5 text-gray-500 transition-colors duration-300 dark:text-slate-400 group-hover:text-yellow-600 dark:group-hover:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>

                
                <div>
                    <div class="text-xs font-bold tracking-wider text-gray-500 uppercase dark:text-slate-400">Periode Dashboard</div>
                    <div
                        x-text="displayText"
                        class="mt-1 text-base font-bold"
                        :class="hasSelection ? 'text-gray-900 dark:text-slate-100' : 'text-gray-400 dark:text-slate-500'"
                    ></div>
                    <div x-show="hasSelection" class="mt-1 text-xs font-medium text-yellow-600 dark:text-yellow-400">
                        ✓ Periode dipilih
                    </div>
                </div>
            </div>

            
            <svg
                class="w-5 h-5 text-gray-400 transition-transform duration-300 dark:text-slate-500"
                :class="{ 'rotate-180': isOpen }"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </button>
    </div>

    
    <div
        x-ref="dropdown"
        x-show="isOpen"
        x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform scale-95 translate-y-2"
        x-transition:enter-end="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform scale-100 translate-y-0"
        x-transition:leave-end="opacity-0 transform scale-95 translate-y-2"
        class="absolute bg-white border border-gray-200 shadow-2xl dark:bg-slate-800 dark:border-slate-600 rounded-2xl dropdown-panel"
        style="min-width: 700px; z-index: 1000; top: 100%; left: 0; margin-top: 8px; min-height: 400px; overflow: visible;"
    >
        <div class="flex overflow-hidden rounded-2xl" style="min-height: 400px;">
            
            <div class="w-56 border-r border-gray-100 dark:border-slate-600 sidebar-gradient">
                <div class="p-5">
                    <div class="flex items-center mb-4">
                        <div class="flex items-center justify-center w-6 h-6 mr-2 bg-yellow-500 rounded-lg dark:bg-yellow-500">
                            <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-sm font-bold text-gray-900 dark:text-slate-100">Filter Cepat</h3>
                    </div>
                    <div class="space-y-1">
                        <template x-for="preset in presets" :key="preset.key">
                            <button
                                @click="selectPreset(preset)"
                                class="w-full px-3 py-2.5 text-sm text-left transition-all duration-200 rounded-lg hover:bg-white dark:hover:bg-slate-700 hover:shadow-sm group relative overflow-hidden"
                                :class="selectedPreset === preset.key
                                    ? 'bg-gradient-to-r from-yellow-500 to-amber-500 dark:from-yellow-500 dark:to-yellow-600 text-white shadow-md'
                                    : 'text-gray-700 dark:text-slate-300 hover:text-gray-900 dark:hover:text-slate-100 bg-white/50 dark:bg-slate-700/50 hover:bg-white dark:hover:bg-slate-700 border border-gray-200 dark:border-slate-600'"
                            >
                                <div class="relative z-10 flex items-center">
                                    <div class="w-2 h-2 mr-3 transition-all duration-200 rounded-full"
                                         :class="selectedPreset === preset.key ? 'bg-white' : 'bg-yellow-400 dark:bg-yellow-400 opacity-0 group-hover:opacity-100'">
                                    </div>
                                    <span x-text="preset.label" class="font-medium"></span>
                                </div>
                            </button>
                        </template>
                    </div>
                </div>

                
                <div class="px-5 pb-5">
                    <div class="pt-4 border-t border-gray-200 dark:border-slate-600">
                        <div class="flex items-center mb-3">
                            <div class="flex items-center justify-center w-5 h-5 mr-2 rounded bg-amber-500 dark:bg-yellow-500">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <h4 class="text-xs font-bold tracking-wide text-gray-900 uppercase dark:text-slate-100">Waktu</h4>
                        </div>

                        
                        <div class="mb-3">
                            <label class="block mb-1 text-xs font-medium text-gray-600 dark:text-slate-400">Mulai</label>
                            <div class="flex space-x-1">
                                <select x-model="timeStart.hour" class="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded dark:bg-slate-700 dark:border-slate-600 focus:ring-1 focus:ring-yellow-500 dark:focus:ring-yellow-400 focus:border-yellow-500 dark:focus:border-yellow-400 dark:text-slate-100">
                                    <template x-for="hour in hours" :key="hour">
                                        <option :value="hour" x-text="hour.toString().padStart(2, '0')"></option>
                                    </template>
                                </select>
                                <span class="flex items-center text-xs text-gray-500 dark:text-slate-400">:</span>
                                <select x-model="timeStart.minute" class="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded dark:bg-slate-700 dark:border-slate-600 focus:ring-1 focus:ring-yellow-500 dark:focus:ring-yellow-400 focus:border-yellow-500 dark:focus:border-yellow-400 dark:text-slate-100">
                                    <template x-for="minute in minutes" :key="minute">
                                        <option :value="minute" x-text="minute.toString().padStart(2, '0')"></option>
                                    </template>
                                </select>
                            </div>
                        </div>

                        
                        <div>
                            <label class="block mb-1 text-xs font-medium text-gray-600 dark:text-slate-400">Berakhir</label>
                            <div class="flex space-x-1">
                                <select x-model="timeEnd.hour" class="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded dark:bg-slate-700 dark:border-slate-600 focus:ring-1 focus:ring-yellow-500 dark:focus:ring-yellow-400 focus:border-yellow-500 dark:focus:border-yellow-400 dark:text-slate-100">
                                    <template x-for="hour in hours" :key="hour">
                                        <option :value="hour" x-text="hour.toString().padStart(2, '0')"></option>
                                    </template>
                                </select>
                                <span class="flex items-center text-xs text-gray-500 dark:text-slate-400">:</span>
                                <select x-model="timeEnd.minute" class="flex-1 px-2 py-1 text-xs bg-white border border-gray-300 rounded dark:bg-slate-700 dark:border-slate-600 focus:ring-1 focus:ring-yellow-500 dark:focus:ring-yellow-400 focus:border-yellow-500 dark:focus:border-yellow-400 dark:text-slate-100">
                                    <template x-for="minute in minutes" :key="minute">
                                        <option :value="minute" x-text="minute.toString().padStart(2, '0')"></option>
                                    </template>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            
            <div class="flex-1 p-4 bg-white dark:bg-slate-800 calendar-bg" style="min-width: 400px;">
                
                <div class="flex items-center justify-between mb-4">
                    <button
                        @click="previousMonth()"
                        class="p-2 text-gray-400 transition-all duration-200 rounded-lg dark:text-slate-500 hover:text-gray-600 dark:hover:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>

                    <div class="flex items-center space-x-8">
                        <h3 class="text-sm font-bold text-gray-900 dark:text-slate-100" x-text="formatMonthYear(currentMonth)"></h3>
                        <h3 class="text-sm font-bold text-gray-900 dark:text-slate-100" x-text="formatMonthYear(nextMonth)"></h3>
                    </div>

                    <button
                        @click="nextMonth()"
                        class="p-2 text-gray-400 transition-all duration-200 rounded-lg dark:text-slate-500 hover:text-gray-600 dark:hover:text-slate-300 hover:bg-gray-100 dark:hover:bg-slate-700"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>

                
                <div class="grid grid-cols-2 gap-6">
                    
                    <div>
                        
                        <div class="grid grid-cols-7 gap-1 mb-2">
                            <template x-for="day in dayNames" :key="day">
                                <div class="p-1 text-xs font-semibold text-center text-gray-500 uppercase dark:text-slate-400" x-text="day"></div>
                            </template>
                        </div>
                        
                        <div class="grid grid-cols-7 gap-1">
                            <template x-for="date in currentMonthDates" :key="date.key">
                                <button
                                    @click="selectDate(date)"
                                    class="relative p-2 text-xs font-medium text-center transition-all duration-200 rounded"
                                    :class="getDateClasses(date)"
                                    :disabled="!date.isCurrentMonth"
                                    x-text="date.day"
                                ></button>
                            </template>
                        </div>
                    </div>

                    
                    <div>
                        
                        <div class="grid grid-cols-7 gap-1 mb-2">
                            <template x-for="day in dayNames" :key="day">
                                <div class="p-1 text-xs font-semibold text-center text-gray-500 uppercase dark:text-slate-400" x-text="day"></div>
                            </template>
                        </div>
                        
                        <div class="grid grid-cols-7 gap-1">
                            <template x-for="date in nextMonthDates" :key="date.key">
                                <button
                                    @click="selectDate(date)"
                                    class="relative p-2 text-xs font-medium text-center transition-all duration-200 rounded"
                                    :class="getDateClasses(date)"
                                    :disabled="!date.isCurrentMonth"
                                    x-text="date.day"
                                ></button>
                            </template>
                        </div>
                    </div>
                </div>

                
                <div class="flex items-center justify-between pt-4 mt-4 border-t border-gray-100 dark:border-slate-600">
                    
                    <div class="text-xs text-gray-600 dark:text-slate-400" x-show="hasSelection">
                        <span class="font-medium">Terpilih:</span>
                        <span x-text="selectedRangeText"></span>
                    </div>
                    <div x-show="!hasSelection" class="text-xs text-gray-400 dark:text-slate-500">
                        Pilih rentang tanggal
                    </div>

                    
                    <div class="flex space-x-2">
                        <button
                            @click="cancel()"
                            class="px-4 py-2 text-xs font-medium text-gray-700 transition-all duration-200 bg-white border border-gray-300 rounded dark:text-slate-300 dark:bg-slate-700 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-600 hover:border-gray-400 dark:hover:border-slate-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                        >
                            Batal
                        </button>
                        <button
                            @click="process()"
                            class="px-4 py-2 text-xs font-medium text-white transition-all duration-200 bg-yellow-600 border border-transparent rounded shadow-sm dark:bg-yellow-600 hover:bg-yellow-700 dark:hover:bg-yellow-700 focus:outline-none focus:ring-1 focus:ring-yellow-500 dark:focus:ring-yellow-400"
                            :disabled="!hasSelection"
                            :class="{ 'opacity-50 cursor-not-allowed hover:bg-yellow-600 dark:hover:bg-yellow-600': !hasSelection }"
                        >
                            Proses
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/*
 * Color Theory-Based Dark Mode for bg-yellow-300 Brand
 * Using complementary blue-gray (slate) as primary dark background
 * Yellow remains as accent color for brand consistency
 */

/*
 * CSS Cascade Strategy:
 * 1. Base light mode styles (default)
 * 2. Dark mode media query overrides
 * 3. Dark mode class overrides (highest specificity)
 */

/* Base styles for light mode - Yellow gradient for brand consistency */
.sidebar-gradient {
    background: linear-gradient(to bottom, #fefce8, #fffbeb, #fff7ed) !important;
}

/* Light mode calendar background */
.calendar-bg {
    background: white !important;
}

/* Dark mode overrides using media query */
@media (prefers-color-scheme: dark) {
    .sidebar-gradient {
        background: linear-gradient(to bottom, #334155, #475569, #1e293b) !important;
    }

    .calendar-bg {
        background: #1e293b !important;
    }
}

/* Dark mode class overrides (highest specificity for manual dark mode toggle) */
.dark .sidebar-gradient {
    background: linear-gradient(to bottom, #334155, #475569, #1e293b) !important;
}

.dark .calendar-bg {
    background: #1e293b !important;
}

/* Ensure light mode is not affected by dark mode styles */
html:not(.dark) .sidebar-gradient {
    background: linear-gradient(to bottom, #fefce8, #fffbeb, #fff7ed) !important;
}

html:not(.dark) .calendar-bg {
    background: white !important;
}
</style>

<script>
function modernDateRangePicker(config) {
    return {
        isOpen: false,
        startDate: null,
        endDate: null,
        tempStartDate: null,
        tempEndDate: null,
        currentMonth: new Date(),
        selectedPreset: null,
        timeStart: { hour: 0, minute: 0 },
        timeEnd: { hour: 23, minute: 59 },
        placeholder: 'Pilih rentang tanggal',

        dayNames: ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'],

        presets: [
            { key: 'today', label: 'Hari Ini' },
            { key: 'yesterday', label: 'Kemarin' },
            { key: '7days', label: '7 Hari Terakhir' },
            { key: 'thisWeek', label: 'Minggu Ini' },
            { key: 'lastWeek', label: 'Minggu Lalu' },
            { key: '30days', label: '30 Hari Terakhir' },
            { key: 'thisMonth', label: 'Bulan Ini' },
            { key: 'lastMonth', label: 'Bulan Lalu' }
        ],

        hours: Array.from({ length: 24 }, (_, i) => i),
        minutes: Array.from({ length: 60 }, (_, i) => i),

        init() {
            this.placeholder = '<?php echo e($placeholder); ?>' || 'Pilih rentang tanggal';

            // Initialize dates if provided
            if (config.startDate && config.startDate !== '' && config.startDate !== 'null') {
                try {
                    this.startDate = new Date(config.startDate);
                    this.tempStartDate = new Date(config.startDate);
                } catch (e) {
                    console.warn('Invalid start date:', config.startDate);
                }
            }
            if (config.endDate && config.endDate !== '' && config.endDate !== 'null') {
                try {
                    this.endDate = new Date(config.endDate);
                    this.tempEndDate = new Date(config.endDate);
                } catch (e) {
                    console.warn('Invalid end date:', config.endDate);
                }
            }

            // Initialize current month
            this.currentMonth = new Date();
            this.currentMonth.setDate(1);

            // Add scroll event listener to close dropdown when scrolling
            setTimeout(() => {
                this.scrollHandler = (event) => {
                    if (this.isOpen) {
                        // Check if scroll is happening on the main page (not within the dropdown)
                        const dropdown = document.querySelector('.dropdown-panel');
                        if (dropdown && !dropdown.contains(event.target)) {
                            this.closeDropdown();
                        }
                    }
                };

                // Listen for scroll events on window and document
                window.addEventListener('scroll', this.scrollHandler, true);
                document.addEventListener('scroll', this.scrollHandler, true);

                // Also listen for scroll on the main content area
                const mainContent = document.querySelector('.fi-main');
                if (mainContent) {
                    mainContent.addEventListener('scroll', this.scrollHandler, true);
                }
            }, 100);
        },

        destroy() {
            // Clean up event listeners
            if (this.scrollHandler) {
                window.removeEventListener('scroll', this.scrollHandler, true);
                document.removeEventListener('scroll', this.scrollHandler, true);

                const mainContent = document.querySelector('.fi-main');
                if (mainContent) {
                    mainContent.removeEventListener('scroll', this.scrollHandler, true);
                }
            }
        },

        get nextMonth() {
            const next = new Date(this.currentMonth);
            next.setMonth(next.getMonth() + 1);
            return next;
        },

        get hasSelection() {
            return this.tempStartDate && this.tempEndDate;
        },

        get displayText() {
            if (!this.hasSelection) {
                return this.placeholder || 'Pilih rentang tanggal';
            }

            const start = this.formatDate(this.tempStartDate);
            const end = this.formatDate(this.tempEndDate);

            if (this.selectedPreset) {
                const preset = this.presets.find(p => p.key === this.selectedPreset);
                return preset ? preset.label : `${start} - ${end}`;
            }

            return `${start} - ${end}`;
        },

        get selectedRangeText() {
            if (!this.hasSelection) return '';

            const start = this.formatDate(this.tempStartDate);
            const end = this.formatDate(this.tempEndDate);
            const startTime = `${this.timeStart.hour.toString().padStart(2, '0')}:${this.timeStart.minute.toString().padStart(2, '0')}`;
            const endTime = `${this.timeEnd.hour.toString().padStart(2, '0')}:${this.timeEnd.minute.toString().padStart(2, '0')}`;

            return `${start} ${startTime} - ${end} ${endTime}`;
        },

        get currentMonthDates() {
            return this.generateCalendarDates(this.currentMonth);
        },

        get nextMonthDates() {
            return this.generateCalendarDates(this.nextMonth);
        },

        toggleDropdown() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.tempStartDate = this.startDate ? new Date(this.startDate) : null;
                this.tempEndDate = this.endDate ? new Date(this.endDate) : null;

                // Position dropdown with absolute positioning (not fixed)
                this.$nextTick(() => {
                    const dropdown = this.$refs.dropdown;
                    if (dropdown) {
                        const viewportWidth = window.innerWidth;
                        const dropdownWidth = 700;
                        const containerRect = dropdown.parentElement.getBoundingClientRect();

                        // Check if dropdown would go off-screen to the right
                        if (containerRect.left + dropdownWidth > viewportWidth) {
                            dropdown.style.left = 'auto';
                            dropdown.style.right = '0';
                        } else {
                            dropdown.style.left = '0';
                            dropdown.style.right = 'auto';
                        }

                        // Ensure it stays absolute positioned
                        dropdown.style.position = 'absolute';
                        dropdown.style.top = '100%';
                        dropdown.style.marginTop = '8px';
                    }
                });
            }
        },

        closeDropdown() {
            this.isOpen = false;
        },

        selectPreset(preset) {
            this.selectedPreset = preset.key;
            const today = new Date();

            switch (preset.key) {
                case 'today':
                    this.tempStartDate = new Date(today);
                    this.tempEndDate = new Date(today);
                    break;
                case 'yesterday':
                    const yesterday = new Date(today);
                    yesterday.setDate(yesterday.getDate() - 1);
                    this.tempStartDate = new Date(yesterday);
                    this.tempEndDate = new Date(yesterday);
                    break;
                case '7days':
                    this.tempEndDate = new Date(today);
                    this.tempStartDate = new Date(today);
                    this.tempStartDate.setDate(this.tempStartDate.getDate() - 6);
                    break;
                case 'thisWeek':
                    const startOfWeek = new Date(today);
                    startOfWeek.setDate(today.getDate() - today.getDay());
                    this.tempStartDate = new Date(startOfWeek);
                    this.tempEndDate = new Date(today);
                    break;
                case 'lastWeek':
                    const lastWeekEnd = new Date(today);
                    lastWeekEnd.setDate(today.getDate() - today.getDay() - 1);
                    const lastWeekStart = new Date(lastWeekEnd);
                    lastWeekStart.setDate(lastWeekEnd.getDate() - 6);
                    this.tempStartDate = new Date(lastWeekStart);
                    this.tempEndDate = new Date(lastWeekEnd);
                    break;
                case '30days':
                    this.tempEndDate = new Date(today);
                    this.tempStartDate = new Date(today);
                    this.tempStartDate.setDate(this.tempStartDate.getDate() - 29);
                    break;
                case 'thisMonth':
                    this.tempStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
                    this.tempEndDate = new Date(today);
                    break;
                case 'lastMonth':
                    const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
                    this.tempStartDate = new Date(lastMonth);
                    this.tempEndDate = new Date(lastMonthEnd);
                    break;
            }
        },

        selectDate(date) {
            if (!date.isCurrentMonth) return;

            const selectedDate = new Date(date.fullDate);

            if (!this.tempStartDate || (this.tempStartDate && this.tempEndDate)) {
                // Start new selection
                this.tempStartDate = selectedDate;
                this.tempEndDate = null;
                this.selectedPreset = null;
            } else if (this.tempStartDate && !this.tempEndDate) {
                // Complete the range
                if (selectedDate >= this.tempStartDate) {
                    this.tempEndDate = selectedDate;
                } else {
                    this.tempEndDate = this.tempStartDate;
                    this.tempStartDate = selectedDate;
                }
                this.selectedPreset = null;
            }
        },

        getDateClasses(date) {
            if (!date.isCurrentMonth) {
                return 'text-gray-300 dark:text-slate-500 cursor-not-allowed opacity-50';
            }

            const isSelected = this.isDateSelected(date);
            const isInRange = this.isDateInRange(date);
            const isToday = this.isToday(date);
            const isStartDate = this.isStartDate(date);
            const isEndDate = this.isEndDate(date);

            let classes = 'hover:bg-yellow-50 dark:hover:bg-slate-600 hover:text-yellow-700 dark:hover:text-slate-100 cursor-pointer ';

            if (isSelected) {
                if (isStartDate || isEndDate) {
                    classes += 'bg-yellow-600 dark:bg-yellow-500 text-white dark:text-gray-900 shadow-md hover:bg-yellow-700 dark:hover:bg-yellow-400 ';
                } else {
                    classes += 'bg-yellow-600 dark:bg-yellow-500 text-white dark:text-gray-900 shadow-md hover:bg-yellow-700 dark:hover:bg-yellow-400 ';
                }
            } else if (isInRange) {
                classes += 'bg-yellow-100 dark:bg-slate-600 text-yellow-800 dark:text-slate-100 ';
            } else if (isToday) {
                classes += 'bg-gray-100 dark:bg-slate-700 text-gray-900 dark:text-white font-bold border-2 border-yellow-300 dark:border-yellow-400 ';
            } else {
                classes += 'text-gray-700 dark:text-slate-200 hover:bg-gray-50 dark:hover:bg-slate-700 ';
            }

            return classes;
        },

        isStartDate(date) {
            if (!this.tempStartDate) return false;
            return date.fullDate === this.formatDateForComparison(this.tempStartDate);
        },

        isEndDate(date) {
            if (!this.tempEndDate) return false;
            return date.fullDate === this.formatDateForComparison(this.tempEndDate);
        },

        isDateSelected(date) {
            if (!this.tempStartDate) return false;

            const dateStr = date.fullDate;
            const startStr = this.formatDateForComparison(this.tempStartDate);
            const endStr = this.tempEndDate ? this.formatDateForComparison(this.tempEndDate) : null;

            return dateStr === startStr || (endStr && dateStr === endStr);
        },

        isDateInRange(date) {
            if (!this.tempStartDate || !this.tempEndDate) return false;

            const dateObj = new Date(date.fullDate);
            return dateObj > this.tempStartDate && dateObj < this.tempEndDate;
        },

        isToday(date) {
            const today = new Date();
            return date.fullDate === this.formatDateForComparison(today);
        },

        generateCalendarDates(month) {
            const dates = [];
            const firstDay = new Date(month.getFullYear(), month.getMonth(), 1);
            const lastDay = new Date(month.getFullYear(), month.getMonth() + 1, 0);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());

            for (let i = 0; i < 42; i++) {
                const date = new Date(startDate);
                date.setDate(startDate.getDate() + i);

                dates.push({
                    day: date.getDate(),
                    fullDate: this.formatDateForComparison(date),
                    isCurrentMonth: date.getMonth() === month.getMonth(),
                    key: `${date.getFullYear()}-${date.getMonth()}-${date.getDate()}`
                });
            }

            return dates;
        },

        formatDate(date) {
            return date.toLocaleDateString('id-ID', {
                day: '2-digit',
                month: '2-digit',
                year: 'numeric'
            });
        },

        formatDateForComparison(date) {
            return date.toISOString().split('T')[0];
        },

        formatMonthYear(date) {
            return date.toLocaleDateString('id-ID', {
                month: 'long',
                year: 'numeric'
            });
        },

        previousMonth() {
            this.currentMonth.setMonth(this.currentMonth.getMonth() - 1);
            this.currentMonth = new Date(this.currentMonth);
        },

        nextMonth() {
            this.currentMonth.setMonth(this.currentMonth.getMonth() + 1);
            this.currentMonth = new Date(this.currentMonth);
        },

        cancel() {
            this.isOpen = false;
            this.tempStartDate = this.startDate ? new Date(this.startDate) : null;
            this.tempEndDate = this.endDate ? new Date(this.endDate) : null;
            this.selectedPreset = null;
        },

        process() {
            if (!this.hasSelection) return;

            this.startDate = new Date(this.tempStartDate);
            this.endDate = new Date(this.tempEndDate);

            // Dispatch event with selected data
            this.$dispatch('daterange-selected', {
                start: this.formatDateForComparison(this.startDate),
                end: this.formatDateForComparison(this.endDate),
                timeStart: `${this.timeStart.hour.toString().padStart(2, '0')}:${this.timeStart.minute.toString().padStart(2, '0')}`,
                timeEnd: `${this.timeEnd.hour.toString().padStart(2, '0')}:${this.timeEnd.minute.toString().padStart(2, '0')}`
            });

            this.isOpen = false;
        }
    }
}
</script>
<?php /**PATH D:\laragon\www\viera\resources\views/components/modern-date-range-picker.blade.php ENDPATH**/ ?>