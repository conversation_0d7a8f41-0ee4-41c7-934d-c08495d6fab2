<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            Alokasi Sumber Daya
         <?php $__env->endSlot(); ?>

        <div class="space-y-6">
            <!-- Summary Stats -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center p-3 bg-blue-50 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600"><?php echo e($totalActiveProjects); ?></div>
                    <div class="text-sm text-blue-700">Kegiatan Berlangsung</div>
                </div>
                <div class="text-center p-3 bg-green-50 rounded-lg">
                    <div class="text-2xl font-bold text-green-600"><?php echo e(number_format($averageUtilization, 1)); ?>%</div>
                    <div class="text-sm text-green-700">Rata-rata Utilisasi</div>
                </div>
                <div class="text-center p-3 bg-purple-50 rounded-lg">
                    <div class="text-2xl font-bold text-purple-600"><?php echo e($totalTeamMembers); ?></div>
                    <div class="text-sm text-purple-700">Anggota Tim</div>
                </div>
                <div class="text-center p-3 bg-red-50 rounded-lg">
                    <div class="text-2xl font-bold text-red-600"><?php echo e($overloadedMembers); ?></div>
                    <div class="text-sm text-red-700">Overloaded</div>
                </div>
            </div>

            <!-- Project Allocation -->
            <div>
                <h4 class="text-lg font-medium text-gray-900 mb-4">Alokasi Sumber Daya</h4>
                <div class="space-y-4">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <div>
                                    <h5 class="font-medium text-gray-900"><?php echo e($project['name']); ?></h5>
                                    <div class="text-sm text-gray-600">
                                        <?php echo e($project['active_members_count']); ?> anggota •
                                        <?php echo e($project['total_capacity']); ?> jam/minggu
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-lg font-semibold text-gray-900"><?php echo e($project['utilization_rate']); ?>%</div>
                                    <div class="text-sm text-gray-500">Utilisasi</div>
                                </div>
                            </div>

                            <!-- Progress Bar -->
                            <div class="mb-3">
                                <div class="flex justify-between text-xs text-gray-600 mb-1">
                                    <span>Progres</span>
                                    <span><?php echo e($project['progress_percentage']); ?>%</span>
                                </div>
                                <div class="w-full bg-gray-200 rounded-full h-2">
                                    <div class="bg-blue-500 h-2 rounded-full"
                                         style="width: <?php echo e($project['progress_percentage']); ?>%"></div>
                                </div>
                            </div>

                            <!-- Task Stats -->
                            <div class="grid grid-cols-3 gap-4 text-center text-sm">
                                <div>
                                    <div class="font-semibold text-gray-900"><?php echo e($project['total_tasks']); ?></div>
                                    <div class="text-gray-500">Total Tugas</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-green-600"><?php echo e($project['completed_tasks']); ?></div>
                                    <div class="text-gray-500">Selesai</div>
                                </div>
                                <div>
                                    <div class="font-semibold text-yellow-600"><?php echo e($project['in_progress_tasks']); ?></div>
                                    <div class="text-gray-500">Sedang Berjalan</div>
                                </div>
                            </div>

                            <!-- Team Members -->
                            <!--[if BLOCK]><![endif]--><?php if($project['members']->isNotEmpty()): ?>
                                <div class="mt-3 pt-3 border-t border-gray-200">
                                    <div class="text-sm font-medium text-gray-700 mb-2">Anggota Tim:</div>
                                    <div class="flex flex-wrap gap-2">
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $project['members']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800">
                                                <?php echo e($member['name']); ?>

                                                <span class="ml-1 text-gray-500">(<?php echo e($member['role']); ?>)</span>
                                            </span>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>

            <!-- Team Workload -->
            <div>
                <h4 class="text-lg font-medium text-gray-900 mb-4">Beban Kerja Tim</h4>
                <div class="space-y-3">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $teamWorkload; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center space-x-3">
                                <div class="w-3 h-3 rounded-full bg-<?php echo e($member['workload_color']); ?>-500"></div>
                                <div>
                                    <div class="font-medium text-gray-900"><?php echo e($member['name']); ?></div>
                                    <div class="text-sm text-gray-600">
                                        <?php echo e($member['active_tasks']); ?> active tasks
                                        <!--[if BLOCK]><![endif]--><?php if($member['overdue_tasks'] > 0): ?>
                                            • <span class="text-red-600"><?php echo e($member['overdue_tasks']); ?> overdue</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-<?php echo e($member['workload_color']); ?>-100 text-<?php echo e($member['workload_color']); ?>-800">
                                    <?php echo e(ucfirst($member['workload_level'])); ?>

                                </span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                    <!--[if BLOCK]><![endif]--><?php if($teamWorkload->isEmpty()): ?>
                        <div class="text-center text-gray-500 py-4">
                            <p>Tidak ada anggota tim dengan task aktif</p>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/widgets/project-resource-allocation.blade.php ENDPATH**/ ?>