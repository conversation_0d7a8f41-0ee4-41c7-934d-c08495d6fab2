<?php

namespace App\Filament\Resources\AbsensiResource\Pages;

use App\Filament\Resources\AbsensiResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Actions;

class ViewAbsensi extends ViewRecord
{
    protected static string $resource = AbsensiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // acc cuti
            Actions\Action::make('acc_absensi')
                ->label('ACC Absensi')
                ->icon('heroicon-o-check')
                ->color('success')
                ->requiresConfirmation()
                ->action(fn() => $this->record->update(
                    [
                        'approved_by' => auth()->id(),
                        'approved_at' => now()
                    ]
                )),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Informasi Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('karyawan.nama_lengkap')
                                    ->label('<PERSON><PERSON>wan'),
                                Infolists\Components\TextEntry::make('tanggal_absensi')
                                    ->label('Tanggal')
                                    ->date('d M Y'),
                                Infolists\Components\TextEntry::make('waktu_masuk')
                                    ->label('Jam Masuk')
                                    ->time('H:i'),
                                Infolists\Components\TextEntry::make('waktu_keluar')
                                    ->label('Jam Keluar')
                                    ->time('H:i')
                                    ->placeholder('-'),
                                Infolists\Components\TextEntry::make('status')
                                    ->label('Status')
                                    ->badge()
                                    ->color(fn(string $state): string => match (strtolower($state)) {
                                        'hadir' => 'success',
                                        'terlambat' => 'warning',
                                        'izin' => 'info',
                                        'sakit' => 'info',
                                        'cuti' => 'primary',
                                        'alpha' => 'danger',
                                        default => 'gray',
                                    }),
                                Infolists\Components\TextEntry::make('jadwal.shift.nama_shift')
                                    ->label('Shift')
                                    ->placeholder('-'),
                            ]),

                        Infolists\Components\TextEntry::make('keterangan')
                            ->label('Keterangan')
                            ->placeholder('-')
                            ->columnSpanFull(),
                    ]),

                Infolists\Components\Section::make('Lokasi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('lokasi_masuk')
                                    ->label('Lokasi Masuk')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                                Infolists\Components\TextEntry::make('lokasi_keluar')
                                    ->label('Lokasi Keluar')
                                    ->placeholder('-')
                                    ->formatStateUsing(function ($state) {
                                        if (!$state) return '-';
                                        $coords = explode(',', $state);
                                        if (count($coords) === 2) {
                                            return "Lat: {$coords[0]}, Lng: {$coords[1]}";
                                        }
                                        return $state;
                                    }),
                            ]),
                    ]),

                Infolists\Components\Section::make('Peta Lokasi')
                    ->schema([
                        Infolists\Components\TextEntry::make('maps_info')
                            ->label('Akses Peta Interaktif')
                            ->formatStateUsing(function ($record) {
                                $content = '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9;">';
                                $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">🗺️ Lokasi Absensi</h4>';

                                if ($record->latitude_masuk && $record->longitude_masuk) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #059669;">📍 Lokasi Masuk:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #ecfdf5; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_masuk . ', ' . $record->longitude_masuk;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_masuk . ',' . $record->longitude_masuk . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                if ($record->latitude_keluar && $record->longitude_keluar) {
                                    $content .= '<div style="margin-bottom: 12px;">';
                                    $content .= '<strong style="color: #dc2626;">📍 Lokasi Keluar:</strong><br>';
                                    $content .= '<span style="font-family: monospace; background: #fef2f2; padding: 4px 8px; border-radius: 4px; font-size: 14px;">';
                                    $content .= $record->latitude_keluar . ', ' . $record->longitude_keluar;
                                    $content .= '</span><br>';
                                    $content .= '<a href="https://www.google.com/maps?q=' . $record->latitude_keluar . ',' . $record->longitude_keluar . '" target="_blank" style="color: #2563eb; text-decoration: underline; font-size: 14px;">🗺️ Buka di Google Maps</a>';
                                    $content .= '</div>';
                                }

                                $content .= '<p style="margin: 12px 0 0 0; padding: 8px; background: #eff6ff; border-radius: 4px; font-size: 13px; color: #1e40af;">';
                                $content .= '💡 <strong>Tips:</strong> Gunakan tombol "Lihat Lokasi" di halaman daftar absensi untuk melihat peta interaktif dengan detail lengkap.';
                                $content .= '</p>';
                                $content .= '</div>';

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => ($record->latitude_masuk && $record->longitude_masuk) || ($record->latitude_keluar && $record->longitude_keluar)),

                Infolists\Components\Section::make('Foto Absensi')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\ImageEntry::make('foto_masuk')
                                    ->label('Foto Masuk')
                                    ->disk('public')
                                    ->height(200)
                                    ->width(200),

                                Infolists\Components\ImageEntry::make('foto_keluar')
                                    ->label('Foto Keluar')
                                    ->disk('public')
                                    ->height(200)
                                    ->width(200),
                            ]),
                    ]),

                Infolists\Components\Section::make('Metadata Foto')
                    ->schema([
                        Infolists\Components\TextEntry::make('metadata_info')
                            ->label('Informasi Metadata')
                            ->formatStateUsing(function ($record) {
                                $content = '';

                                // Metadata foto masuk
                                if ($record->metadata_foto_masuk) {
                                    $metadata = $record->metadata_foto_masuk;
                                    $content .= '<div style="background: #f0f9ff; padding: 16px; border-radius: 8px; border: 1px solid #0ea5e9; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #0c4a6e;">📸 Metadata Foto Masuk</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                // Metadata foto keluar
                                if ($record->metadata_foto_keluar) {
                                    $metadata = $record->metadata_foto_keluar;
                                    $content .= '<div style="background: #fef2f2; padding: 16px; border-radius: 8px; border: 1px solid #f87171; margin-bottom: 16px;">';
                                    $content .= '<h4 style="margin: 0 0 12px 0; font-weight: 600; color: #7f1d1d;">🏠 Metadata Foto Keluar</h4>';

                                    if (isset($metadata['datetime_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>🕐 Waktu:</strong> ' . $metadata['datetime_display'] . '</div>';
                                    }
                                    if (isset($metadata['coordinates_display'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📍 Koordinat:</strong> ' . $metadata['coordinates_display'] . '</div>';
                                    }
                                    if (isset($metadata['status_kehadiran'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>⏰ Status:</strong> ' . $metadata['status_kehadiran'] . '</div>';
                                    }
                                    if (isset($metadata['camera_info'])) {
                                        $content .= '<div style="margin-bottom: 8px;"><strong>📱 Device:</strong> ' . $metadata['camera_info'] . '</div>';
                                    }
                                    $content .= '</div>';
                                }

                                if (empty($content)) {
                                    $content = '<div style="color: #9ca3af; font-style: italic;">Tidak ada metadata foto tersedia</div>';
                                }

                                return new \Illuminate\Support\HtmlString($content);
                            })
                            ->html()
                            ->columnSpanFull(),
                    ])
                    ->visible(fn($record) => !empty($record->metadata_foto_masuk) || !empty($record->metadata_foto_keluar)),

                Infolists\Components\Section::make('Persetujuan')
                    ->schema([
                        Infolists\Components\Grid::make(2)
                            ->schema([
                                Infolists\Components\TextEntry::make('approvedBy.name')
                                    ->label('Disetujui Oleh')
                                    ->placeholder('Belum disetujui'),
                                Infolists\Components\TextEntry::make('approved_at')
                                    ->label('Tanggal Persetujuan')
                                    ->dateTime('d M Y H:i')
                                    ->placeholder('Belum disetujui'),
                            ]),
                    ]),
            ]);
    }
}
