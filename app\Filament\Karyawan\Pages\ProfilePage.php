<?php

namespace App\Filament\Karyawan\Pages;

use App\Models\User;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;

class ProfilePage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-user';
    protected static ?string $navigationLabel = 'Edit Profil';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'profile';
    protected static ?string $title = 'Profil Saya';
    protected static string $view = 'filament.karyawan.pages.profile';

    public ?array $data = [];

    public function mount(): void
    {
        $user = Auth::user();

        $this->form->fill([
            'name' => $user->name,
            'email' => $user->email,
        ]);
    }

    // hasaccess
    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user?->karyawan()->exists();
    }

    public static function canCreate(): bool
    {
        return Auth::user()->karyawan()->exists();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Informasi Akun')
                    ->description('Perbarui informasi akun Anda')
                    ->schema([
                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\FileUpload::make('foto_profil')
                                    ->label('Foto Profil')
                                    ->image()
                                    ->directory('karyawan/profile')
                                    ->disk('public')
                                    ->columnSpan(2)
                                    ->helperText('Upload foto profil Anda (opsional)')
                                    ->default(function () {
                                        $user = Auth::user();
                                        $karyawan = $user->karyawan;
                                        return $karyawan ? $karyawan->foto_profil : null;
                                    }),

                                Forms\Components\TextInput::make('name')
                                    ->label('Nama')
                                    ->required()
                                    ->maxLength(255),

                                Forms\Components\TextInput::make('email')
                                    ->label('Email')
                                    ->email()
                                    ->required()
                                    ->maxLength(255),
                            ]),
                    ]),

                Forms\Components\Section::make('Ubah Password')
                    ->description('Perbarui password akun Anda')
                    ->schema([
                        Forms\Components\TextInput::make('current_password')
                            ->label('Password Saat Ini')
                            ->password()
                            ->helperText('Isi hanya jika ingin mengubah password'),

                        Forms\Components\Grid::make(2)
                            ->schema([
                                Forms\Components\TextInput::make('password')
                                    ->label('Password Baru')
                                    ->password()
                                    ->helperText('Minimal 8 karakter')
                                    ->minLength(8)
                                    ->confirmed(),

                                Forms\Components\TextInput::make('password_confirmation')
                                    ->label('Konfirmasi Password Baru')
                                    ->password(),
                            ]),
                    ])
                    ->collapsible(),

                Forms\Components\Section::make('Informasi Karyawan')
                    ->description('Informasi detail karyawan')
                    ->schema([
                        Forms\Components\Placeholder::make('nip')
                            ->label('NIP')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan ? $karyawan->nip : '-';
                            }),

                        Forms\Components\Placeholder::make('nama_lengkap')
                            ->label('Nama Lengkap')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan ? $karyawan->nama_lengkap : '-';
                            }),

                        Forms\Components\Placeholder::make('jabatan')
                            ->label('Jabatan')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan && $karyawan->jabatan ? $karyawan->jabatan->nama_jabatan : '-';
                            }),

                        Forms\Components\Placeholder::make('divisi')
                            ->label('Divisi')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan && $karyawan->divisi ? $karyawan->divisi->nama_divisi : '-';
                            }),

                        Forms\Components\Placeholder::make('departemen')
                            ->label('Departemen')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan && $karyawan->divisi && $karyawan->divisi->departemen ? $karyawan->divisi->departemen->nama_departemen : '-';
                            }),

                        Forms\Components\Placeholder::make('entitas')
                            ->label('Entitas')
                            ->content(function () {
                                $user = Auth::user();
                                $karyawan = $user->karyawan;
                                return $karyawan && $karyawan->entitas ? $karyawan->entitas->nama : '-';
                            }),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ])
            ->statePath('data');
    }

    protected function getFormActions(): array
    {
        return [
            Action::make('save')
                ->label('Simpan Perubahan')
                ->submit('save'),
        ];
    }

    public function save(): void
    {
        $data = $this->form->getState();

        $authUser = Auth::user();
        $user = User::find($authUser->id);

        // Validate current password if trying to change password
        if (!empty($data['password'])) {
            if (empty($data['current_password'])) {
                Notification::make()
                    ->title('Password saat ini diperlukan')
                    ->body('Silahkan masukkan password saat ini untuk mengubah password.')
                    ->danger()
                    ->send();

                return;
            }

            if (!Hash::check($data['current_password'], $user->password)) {
                Notification::make()
                    ->title('Password saat ini salah')
                    ->body('Password saat ini yang Anda masukkan tidak sesuai.')
                    ->danger()
                    ->send();

                return;
            }

            // Set the new password
            $user->update([
                'password' => Hash::make($data['password']),
            ]);
        }

        // Update user data
        $user->update([
            'name' => $data['name'],
            'email' => $data['email'],
        ]);

        // Update karyawan data if exists
        if ($user->karyawan && isset($data['foto_profil'])) {
            $user->karyawan->update([
                'foto_profil' => $data['foto_profil'],
            ]);
        }

        Notification::make()
            ->title('Profil berhasil diperbarui')
            ->body('Data profil Anda telah berhasil diperbarui.')
            ->success()
            ->send();
    }
}
