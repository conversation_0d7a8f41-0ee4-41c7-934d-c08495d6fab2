<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
         <?php $__env->slot('heading', null, []); ?> 
            📋 SOP Terbaru
         <?php $__env->endSlot(); ?>

         <?php $__env->slot('description', null, []); ?> 
            Standard Operating Procedure yang berlaku untuk Anda
         <?php $__env->endSlot(); ?>

        <?php
            $sopData = $this->getSopData();
            $karyawan = $this->getKaryawanData();
        ?>

        <div class="space-y-4">
            <!--[if BLOCK]><![endif]--><?php if($sopData->isEmpty()): ?>
                <div class="py-8 text-center">
                    <div class="flex items-center justify-center w-12 h-12 mx-auto bg-gray-100 rounded-full dark:bg-gray-800">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">Belum Ada SOP</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Belum ada SOP yang tersedia untuk departemen atau divisi Anda.
                    </p>
                </div>
            <?php else: ?>
                <!--[if BLOCK]><![endif]--><?php if($karyawan): ?>
                    <div class="p-3 mb-4 rounded-lg bg-blue-50 dark:bg-blue-900/20">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-blue-700 dark:text-blue-300">
                                    <span class="font-medium"><?php echo e($karyawan->nama_lengkap); ?></span> -
                                    <!--[if BLOCK]><![endif]--><?php if($karyawan->departemen): ?>
                                        Departemen: <?php echo e($karyawan->departemen->nama_departemen); ?>

                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($karyawan->divisi): ?>
                                        <!--[if BLOCK]><![endif]--><?php if($karyawan->departemen): ?>, <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        Divisi: <?php echo e($karyawan->divisi->nama_divisi); ?>

                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                <div class="space-y-3">
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $sopData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sop): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="p-4 transition-colors border border-gray-200 rounded-lg dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50">
                            <div class="flex items-start justify-between">
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center mb-2 space-x-2">
                                        <h4 class="text-sm font-medium text-gray-900 truncate dark:text-gray-100">
                                            <?php echo e($sop->judul_sop); ?>

                                        </h4>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                            <?php echo e($sop->scope_type === 'departemen' ? 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300' : 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-300'); ?>">
                                            <?php echo e($sop->scope_type === 'departemen' ? '🏢 Dept' : '👥 Div'); ?>

                                        </span>
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300">
                                            v<?php echo e($sop->versi); ?>

                                        </span>
                                    </div>

                                    <p class="mb-2 text-xs text-gray-600 dark:text-gray-400">
                                        <?php echo e($sop->scope_name); ?>

                                    </p>

                                    <!--[if BLOCK]><![endif]--><?php if($sop->deskripsi): ?>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2">
                                            <?php echo e(Str::limit($sop->deskripsi, 100)); ?>

                                        </p>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                    <div class="flex items-center mt-2 space-x-4 text-xs text-gray-500 dark:text-gray-400">
                                        <span>📅 <?php echo e($sop->tanggal_berlaku->format('d/m/Y')); ?></span>
                                        <!--[if BLOCK]><![endif]--><?php if($sop->tanggal_berakhir): ?>
                                            <span>⏰ s/d <?php echo e($sop->tanggal_berakhir->format('d/m/Y')); ?></span>
                                        <?php else: ?>
                                            <span>⏰ Tidak terbatas</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </div>
                                </div>

                                <div class="flex-shrink-0 ml-4">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(asset('storage/' . $sop->file_path)); ?>"
                                           target="_blank"
                                           class="inline-flex items-center px-2 py-1 text-xs font-medium text-blue-700 transition-colors bg-blue-100 border border-transparent rounded hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            Lihat
                                        </a>

                                        <a href="<?php echo e(asset('storage/' . $sop->file_path)); ?>"
                                           download
                                           class="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 transition-colors bg-green-100 border border-transparent rounded hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/40">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            Download
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </div>

                <div class="mt-4 text-center">
                    <a href="<?php echo e(route('filament.karyawan.resources.sops.index')); ?>"
                       class="inline-flex items-center px-4 py-2 text-sm font-medium text-blue-700 transition-colors bg-blue-100 border border-transparent rounded-md hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:hover:bg-blue-900/40">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Lihat Semua SOP
                    </a>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/karyawan/widgets/sop-widget.blade.php ENDPATH**/ ?>