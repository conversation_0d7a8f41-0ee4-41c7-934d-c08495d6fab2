<?php if (isset($component)) { $__componentOriginalb525200bfa976483b4eaa0b7685c6e24 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-widgets::components.widget','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-widgets::widget'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div <?php if(!$isVisible): ?> style="display: none;" <?php endif; ?>>
        <?php if (isset($component)) { $__componentOriginalee08b1367eba38734199cf7829b1d1e9 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalee08b1367eba38734199cf7829b1d1e9 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.section.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::section'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('heading', null, []); ?> 
                Project Gantt Chart
             <?php $__env->endSlot(); ?>

             <?php $__env->slot('headerEnd', null, []); ?> 
                <div class="flex gap-2">
                    <select wire:model.live="selectedProjectId"
                        class="text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 rounded-md">
                        <option value="">All Projects</option>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = \App\Models\Project::all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </select>
                </div>
             <?php $__env->endSlot(); ?>

            <div class="overflow-x-auto bg-white dark:bg-gray-900 rounded-lg shadow-sm">
                <!--[if BLOCK]><![endif]--><?php if($projects->isEmpty()): ?>
                    <div class="text-center py-12 text-gray-500 dark:text-gray-400">
                        <?php if (isset($component)) { $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c = $attributes; } ?>
<?php $component = BladeUI\Icons\Components\Svg::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('heroicon-o-chart-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(BladeUI\Icons\Components\Svg::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'w-16 h-16 mx-auto mb-4 opacity-50']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $attributes = $__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__attributesOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c)): ?>
<?php $component = $__componentOriginal643fe1b47aec0b76658e1a0200b34b2c; ?>
<?php unset($__componentOriginal643fe1b47aec0b76658e1a0200b34b2c); ?>
<?php endif; ?>
                        <p class="text-lg font-medium">No projects found</p>
                        <p class="text-sm mt-1">Create a project to see the Gantt chart</p>
                    </div>
                <?php else: ?>
                    <div class="min-w-[800px]">
                        
                        <div
                            class="flex border-b-2 border-gray-200 dark:border-gray-700 mb-6 bg-gray-50 dark:bg-gray-800 rounded-t">
                            <div class="w-64 p-3 font-semibold text-gray-900 dark:text-white flex-shrink-0">
                                Project / Task
                            </div>
                            <div class="flex-1 flex">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $timelineData['months']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $month): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex-1 text-center p-3 border-l border-gray-200 dark:border-gray-700">
                                        <div class="text-sm font-semibold text-gray-900 dark:text-white">
                                            <?php echo e($month['name']); ?>

                                        </div>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $timelineData['projectsData']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $projectData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            
                            <div
                                class="flex items-center mb-4 group border-b border-gray-100 dark:border-gray-700 pb-2">
                                <div class="w-64 p-2 flex-shrink-0">
                                    <div class="flex items-center">
                                        <div
                                            class="w-3 h-3 rounded-full mr-2
                                        <?php if($projectData['status'] === 'active'): ?> bg-green-500
                                        <?php elseif($projectData['status'] === 'completed'): ?> bg-blue-500
                                        <?php elseif($projectData['status'] === 'on_hold'): ?> bg-yellow-500
                                        <?php else: ?> bg-gray-500 <?php endif; ?>">
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <div class="font-semibold text-gray-900 dark:text-white truncate">
                                                <?php echo e($projectData['name']); ?>

                                            </div>
                                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                                <?php echo e($projectData['progress']); ?>% complete
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex-1 relative h-8 bg-gray-50 dark:bg-gray-800 rounded">
                                    
                                    <div class="absolute top-1 h-6 rounded
                                        <?php if($projectData['status'] === 'active'): ?> bg-green-400
                                        <?php elseif($projectData['status'] === 'completed'): ?> bg-blue-400
                                        <?php elseif($projectData['status'] === 'on_hold'): ?> bg-yellow-400
                                        <?php else: ?> bg-gray-400 <?php endif; ?> opacity-80"
                                        style="left: <?php echo e(($projectData['startOffset'] / $timelineData['totalDays']) * 100); ?>%;
                                           width: <?php echo e(($projectData['duration'] / $timelineData['totalDays']) * 100); ?>%;">
                                        
                                        <div class="h-full rounded
                                            <?php if($projectData['status'] === 'active'): ?> bg-green-600
                                            <?php elseif($projectData['status'] === 'completed'): ?> bg-blue-600
                                            <?php elseif($projectData['status'] === 'on_hold'): ?> bg-yellow-600
                                            <?php else: ?> bg-gray-600 <?php endif; ?>"
                                            style="width: <?php echo e($projectData['progress']); ?>%;"></div>
                                    </div>
                                </div>
                            </div>

                            
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $projectData['tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $taskData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-start mb-3 ml-4 min-h-[3rem]">
                                    <div class="w-64 p-2 flex-shrink-0">
                                        <div class="flex items-start">
                                            <div
                                                class="w-2 h-2 rounded-full mr-2 mt-1 flex-shrink-0
                                            <?php if($taskData['status'] === 'completed'): ?> bg-green-400
                                            <?php elseif($taskData['status'] === 'in_progress'): ?> bg-blue-400
                                            <?php elseif($taskData['status'] === 'todo'): ?> bg-gray-400
                                            <?php else: ?> bg-yellow-400 <?php endif; ?>">
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <div
                                                    class="text-sm font-medium text-gray-700 dark:text-gray-300 truncate">
                                                    <?php echo e($taskData['name']); ?>

                                                </div>
                                                <!--[if BLOCK]><![endif]--><?php if($taskData['start_date'] || $taskData['due_date']): ?>
                                                    <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                        <!--[if BLOCK]><![endif]--><?php if($taskData['start_date']): ?>
                                                            <div>Start: <?php echo e($taskData['start_date']); ?></div>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                        <!--[if BLOCK]><![endif]--><?php if($taskData['due_date']): ?>
                                                            <div>Due: <?php echo e($taskData['due_date']); ?></div>
                                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                    </div>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex-1 relative h-6 bg-gray-50 dark:bg-gray-800 rounded mt-1">
                                        
                                        <div class="absolute top-1 h-4 rounded cursor-pointer
                                            <?php if($taskData['status'] === 'completed'): ?> bg-green-300
                                            <?php elseif($taskData['status'] === 'in_progress'): ?> bg-blue-300
                                            <?php elseif($taskData['status'] === 'todo'): ?> bg-gray-300
                                            <?php else: ?> bg-yellow-300 <?php endif; ?> opacity-80"
                                            style="left: <?php echo e(($taskData['startOffset'] / $timelineData['totalDays']) * 100); ?>%;
                                               width: <?php echo e(($taskData['duration'] / $timelineData['totalDays']) * 100); ?>%;"
                                            title="Task: <?php echo e($taskData['name']); ?>

<?php if($taskData['start_date']): ?> Start: <?php echo e($taskData['start_date']); ?> <?php endif; ?>
<?php if($taskData['due_date']): ?> Due: <?php echo e($taskData['due_date']); ?> <?php endif; ?>
Status: <?php echo e(ucfirst(str_replace('_', ' ', $taskData['status']))); ?>

Progress: <?php echo e($taskData['progress']); ?>%">
                                            
                                            <div class="h-full rounded
                                                <?php if($taskData['status'] === 'completed'): ?> bg-green-500
                                                <?php elseif($taskData['status'] === 'in_progress'): ?> bg-blue-500
                                                <?php elseif($taskData['status'] === 'todo'): ?> bg-gray-500
                                                <?php else: ?> bg-yellow-500 <?php endif; ?>"
                                                style="width: <?php echo e($taskData['progress']); ?>%;"></div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->

                            <div class="mb-6"></div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    
                    <div class="mt-6 flex flex-wrap gap-4 text-sm">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Completed</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-blue-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Active</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-yellow-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">On Hold</span>
                        </div>
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-500 rounded mr-2"></div>
                            <span class="text-gray-600 dark:text-gray-400">Planned</span>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $attributes = $__attributesOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__attributesOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalee08b1367eba38734199cf7829b1d1e9)): ?>
<?php $component = $__componentOriginalee08b1367eba38734199cf7829b1d1e9; ?>
<?php unset($__componentOriginalee08b1367eba38734199cf7829b1d1e9); ?>
<?php endif; ?>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $attributes = $__attributesOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__attributesOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24)): ?>
<?php $component = $__componentOriginalb525200bfa976483b4eaa0b7685c6e24; ?>
<?php unset($__componentOriginalb525200bfa976483b4eaa0b7685c6e24); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/widgets/project-gantt-chart.blade.php ENDPATH**/ ?>