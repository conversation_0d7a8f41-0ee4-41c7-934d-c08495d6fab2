<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JadwalMasalResource\Pages;
use App\Models\JadwalMasal;
use App\Models\Karyawan;
use App\Models\Shift;
use App\Models\User;
use App\Models\Entitas;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Tables\Columns\TextColumn;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Auth;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;

class JadwalMasalResource extends Resource
{
    protected static ?string $model = JadwalMasal::class;

    protected static ?string $navigationIcon = 'heroicon-o-calendar';
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    protected static ?string $navigationLabel = 'Jadwal Masal';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Informasi Jadwal')
                    ->schema([
                        TextInput::make('nama_jadwal')
                            ->label('Nama Jadwal')
                            ->required()
                            ->maxLength(255),

                        DatePicker::make('tanggal_mulai')
                            ->label('Tanggal Mulai')
                            ->required(),

                        DatePicker::make('tanggal_selesai')
                            ->label('Tanggal Selesai')
                            ->required()
                            ->minDate(fn(Forms\Get $get) => $get('tanggal_mulai')),

                        Select::make('shift_id')
                            ->label('Shift')
                            ->options(Shift::where('is_active', true)->pluck('nama_shift', 'id'))
                            ->required()
                            ->searchable(),

                        Textarea::make('keterangan')
                            ->label('Keterangan')
                            ->maxLength(1000),
                    ])->columns(2),

                Section::make('Pilih Karyawan')
                    ->schema([
                        Select::make('entitas_filter')
                            ->label('Filter berdasarkan Entitas')
                            ->placeholder('Pilih entitas untuk memfilter karyawan')
                            ->options(Entitas::orderBy('nama')->pluck('nama', 'id'))
                            ->searchable()
                            ->reactive()
                            ->afterStateUpdated(fn(callable $set) => $set('karyawan', []))
                            ->helperText('Pilih entitas untuk menampilkan karyawan yang bekerja di entitas tersebut'),

                        Actions::make([
                            Action::make('select_all_employees')
                                ->label('Pilih Semua Karyawan')
                                ->icon('heroicon-o-check-circle')
                                ->color('success')
                                ->action(function (callable $set, callable $get) {
                                    $entitasId = $get('entitas_filter');

                                    // Get all available employee IDs based on current filter
                                    $user = Auth::user();
                                    $query = Karyawan::query()
                                        ->where('status_aktif', 1)
                                        ->orderBy('nama_lengkap');

                                    if ($user->role === 'supervisor') {
                                        $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
                                        $query->whereIn('id', $employeeIds);
                                    }

                                    // Filter by entitas if selected
                                    if ($entitasId) {
                                        $query->where('id_entitas', $entitasId);
                                    }

                                    $allEmployeeIds = $query->pluck('id')->toArray();
                                    $set('karyawan', $allEmployeeIds);
                                })
                                ->visible(fn(callable $get) => $get('entitas_filter') !== null),

                            Action::make('deselect_all_employees')
                                ->label('Hapus Semua Pilihan')
                                ->icon('heroicon-o-x-circle')
                                ->color('danger')
                                ->action(function (callable $set) {
                                    $set('karyawan', []);
                                })
                                ->visible(fn(callable $get) => !empty($get('karyawan'))),
                        ])
                            ->fullWidth()
                            ->alignment('start'),

                        CheckboxList::make('karyawan')
                            ->label('Pilih Karyawan')
                            ->options(function (callable $get) {
                                $entitasId = $get('entitas_filter');

                                // If user is supervisor, only show their supervised employees
                                $user = Auth::user();
                                $query = Karyawan::query()
                                    ->where('status_aktif', 1)
                                    ->orderBy('nama_lengkap');

                                if ($user->role === 'supervisor') {
                                    $employeeIds = Karyawan::where('supervisor_id', $user->id)->pluck('id')->toArray();
                                    $query->whereIn('id', $employeeIds);
                                }

                                // Filter by entitas if selected
                                if ($entitasId) {
                                    $query->where('id_entitas', $entitasId);
                                }

                                return $query->pluck('nama_lengkap', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->columns(3)
                            ->helperText(function (callable $get) {
                                $entitasId = $get('entitas_filter');
                                if (!$entitasId) {
                                    return '💡 Pilih entitas terlebih dahulu untuk memfilter karyawan berdasarkan tempat kerja mereka.';
                                }

                                $entitas = Entitas::find($entitasId);
                                $count = Karyawan::where('status_aktif', 1)
                                    ->where('id_entitas', $entitasId)
                                    ->count();

                                $selectedCount = count($get('karyawan') ?? []);
                                return "📍 Menampilkan {$count} karyawan aktif dari {$entitas->nama} | ✅ {$selectedCount} karyawan dipilih";
                            }),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('nama_jadwal')
                    ->label('Nama Jadwal')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('tanggal_mulai')
                    ->label('Tanggal Mulai')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('tanggal_selesai')
                    ->label('Tanggal Selesai')
                    ->date('d M Y')
                    ->sortable(),

                TextColumn::make('shift.nama_shift')
                    ->label('Shift')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('karyawan_count')
                    ->label('Jumlah Karyawan')
                    ->counts('karyawan')
                    ->sortable(),

                TextColumn::make('entitas_info')
                    ->label('Entitas')
                    ->getStateUsing(function (JadwalMasal $record) {
                        $entitasIds = $record->karyawan()
                            ->whereNotNull('id_entitas')
                            ->distinct('id_entitas')
                            ->pluck('id_entitas');

                        if ($entitasIds->isEmpty()) {
                            return 'Tidak ada entitas';
                        }

                        $entitasNames = Entitas::whereIn('id', $entitasIds)
                            ->pluck('nama')
                            ->take(2);

                        if ($entitasIds->count() > 2) {
                            return $entitasNames->join(', ') . ' +' . ($entitasIds->count() - 2) . ' lainnya';
                        }

                        return $entitasNames->join(', ');
                    })
                    ->badge()
                    ->color('info')
                    ->searchable(false),

                TextColumn::make('creator.name')
                    ->label('Dibuat Oleh')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('created_at')
                    ->label('Dibuat Pada')
                    ->dateTime('d M Y H:i')
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\Action::make('generate')
                    ->label('Generate Jadwal')
                    ->icon('heroicon-o-play-circle')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Generate Jadwal Individual')
                    ->modalDescription('Apakah Anda yakin ingin membuat jadwal individual untuk semua karyawan dalam periode ini?')
                    ->modalSubmitActionLabel('Ya, Generate')
                    ->action(function (JadwalMasal $record) {
                        try {
                            $result = $record->generateSchedules();

                            Notification::make()
                                ->title('Jadwal berhasil digenerate!')
                                ->body("✅ {$result['generated']} jadwal baru dibuat\n⚠️ {$result['skipped']} jadwal sudah ada\n📅 {$result['total_days']} hari untuk {$result['total_employees']} karyawan")
                                ->success()
                                ->duration(8000)
                                ->send();
                        } catch (\Exception $e) {
                            Notification::make()
                                ->title('Error saat generate jadwal')
                                ->body('Error: ' . $e->getMessage())
                                ->danger()
                                ->send();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJadwalMasals::route('/'),
            'create' => Pages\CreateJadwalMasal::route('/create'),
            'edit' => Pages\EditJadwalMasal::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with([
                'shift:id,nama_shift,waktu_mulai,waktu_selesai',
                'creator:id,name',
                'karyawan:id,nama_lengkap'
            ]);

        // If user is supervisor, only show their created bulk schedules
        $user = Auth::user();
        if ($user && $user->role === 'supervisor') {
            $query->where('created_by', $user->id);
        }

        return $query;
    }
}
