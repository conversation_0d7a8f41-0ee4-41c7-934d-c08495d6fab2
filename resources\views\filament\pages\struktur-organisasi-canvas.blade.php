<x-filament-panels::page @class(['fi-struktur-organisasi-canvas-page'])>
    <style>
        .canvas-container {
            width: 100%;
            height: 80vh;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
            background: #f8fafc;
            cursor: grab;
        }

        .canvas-container:active {
            cursor: grabbing;
        }

        .canvas-content {
            position: absolute;
            top: 0;
            left: 0;
            transform-origin: 0 0;
            transition: transform 0.1s ease-out;
            min-width: 2000px;
            min-height: 2200px;
            /* Increased to 2200px for vertical jabatan layout */
            padding: 50px;
        }

        .org-node {
            position: absolute;
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
            min-width: 200px;
            text-align: center;
        }

        .org-node:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
            border-color: #3b82f6;
        }

        .org-node.entitas {
            background: #1f2937;
            color: white;
            font-size: 18px;
            font-weight: bold;
            min-width: 300px;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
        }

        .org-node.departemen {
            background: #374151;
            color: white;
            font-weight: 600;
        }

        .org-node.divisi {
            background: #6b7280;
            color: white;
            font-weight: 500;
        }

        .org-node.jabatan {
            background: #9ca3af;
            color: #1f2937;
            font-weight: 500;
        }

        .connection-line {
            position: absolute;
            background: #6b7280;
            z-index: 1;
        }

        .vertical-line {
            width: 2px;
            background: #6b7280;
        }

        .horizontal-line {
            height: 2px;
            background: #6b7280;
        }

        .connector-dot {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #6b7280;
            border-radius: 50%;
            z-index: 2;
        }

        .canvas-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 100;
            display: flex;
            gap: 10px;
        }

        .canvas-btn {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 500;
        }

        .canvas-btn:hover {
            background: #f3f4f6;
            border-color: #3b82f6;
        }

        .zoom-info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }

        .node-details {
            margin-top: 8px;
            font-size: 12px;
            opacity: 0.8;
        }

        .employee-count {
            background: rgba(255, 255, 255, 0.15);
            padding: 4px 8px;
            border-radius: 6px;
            font-size: 11px;
            margin-top: 6px;
            display: inline-block;
        }

        .node-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s ease;
            max-width: 200px;
        }

        .node-tooltip.show {
            opacity: 1;
        }

        .minimap {
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 200px;
            height: 150px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
        }

        .minimap-content {
            width: 100%;
            height: 100%;
            transform-origin: 0 0;
            transform: scale(0.1);
        }

        .minimap-viewport {
            position: absolute;
            border: 2px solid #3b82f6;
            background: rgba(59, 130, 246, 0.1);
            pointer-events: none;
        }

        .canvas-loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255, 255, 255, 0.9);
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .org-node {
            opacity: 1;
        }
    </style>

    <div class="space-y-6">
        @php
            // Get all departments that have employees in this entity
            $departemen = \App\Models\Departemen::whereHas('karyawan', function ($query) use ($entitas) {
                $query->where('id_entitas', $entitas->id)->where('status_aktif', true);
            })
                ->with([
                    'karyawan' => function ($query) use ($entitas) {
                        $query
                            ->where('id_entitas', $entitas->id)
                            ->where('status_aktif', true)
                            ->with(['divisi', 'jabatan']);
                    },
                ])
                ->get();
        @endphp

        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">
                📊 Canvas Struktur Organisasi: {{ $entitas->nama }}
            </h2>
            <div class="text-sm text-gray-600 dark:text-gray-400">
                <div>Drag untuk navigasi • Scroll untuk zoom • Klik node untuk detail</div>
                <div class="mt-1 text-xs">
                    Shortcuts: R (reset) • +/- (zoom) • F (fit screen)
                </div>
            </div>
        </div>

        <!-- Search Box for Canvas -->
        <div class="mb-4">
            <div class="relative max-w-md">
                <input type="text" id="canvasSearch" placeholder="Cari dalam canvas..."
                    class="w-full px-4 py-2 pl-10 pr-4 text-gray-700 bg-white border border-gray-300 rounded-lg dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
                <button id="clearCanvasSearch"
                    class="absolute inset-y-0 right-0 items-center hidden pr-3 text-gray-400 hover:text-gray-600">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12">
                        </path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Canvas Container -->
        <div class="canvas-container" id="canvasContainer">
            <!-- Canvas Controls -->
            <div class="canvas-controls">
                <button class="canvas-btn" onclick="resetView()">🏠 Reset View</button>
                <button class="canvas-btn" onclick="zoomIn()">🔍 Zoom In</button>
                <button class="canvas-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                <button class="canvas-btn" onclick="fitToScreen()">📐 Fit Screen</button>
            </div>

            <!-- Zoom Info -->
            <div class="zoom-info" id="zoomInfo">
                Zoom: 100% | Pan: 0, 0
            </div>

            <!-- Minimap -->
            <div class="minimap" id="minimap">
                <div class="minimap-content" id="minimapContent">
                    <!-- Minimap nodes will be generated by JavaScript -->
                </div>
                <div class="minimap-viewport" id="minimapViewport"></div>
            </div>

            <!-- Tooltip -->
            <div class="node-tooltip" id="nodeTooltip"></div>

            <!-- Loading State -->
            <div class="canvas-loading" id="canvasLoading">
                <div class="loading-spinner"></div>
                <div class="text-sm font-medium text-gray-700">Memuat struktur organisasi...</div>
            </div>

            <!-- Canvas Content -->
            <div class="canvas-content" id="canvasContent">
                <!-- Entitas Node -->
                <div class="org-node entitas" data-type="entitas" data-id="{{ $entitas->id }}">
                    <div class="text-2xl mb-2">🏢</div>
                    <div class="font-bold">{{ $entitas->nama }}</div>
                    <div class="node-details">
                        {{ $entitas->karyawan()->where('status_aktif', true)->count() }} Total Karyawan
                    </div>
                </div>

                @php
                    $yOffset = 250;
                    $deptIndex = 0;
                    $totalDepts = $departemen->count();

                    // Calculate spacing based on max divisions per department
                    $maxDivisiCount = 0;
                    foreach ($departemen as $dept) {
                        $divisiCount = $dept->karyawan->groupBy('id_divisi')->count();
                        if ($divisiCount > $maxDivisiCount) {
                            $maxDivisiCount = $divisiCount;
                        }
                    }

                    // Calculate dynamic spacing: base 400px + extra space for divisions
                    $baseSpacing = 400;
                    $extraSpacing = max(0, ($maxDivisiCount - 2) * 100); // Extra 100px per division beyond 2
                    $deptSpacing = $baseSpacing + $extraSpacing;
                @endphp

                @foreach ($departemen as $dept)
                    @php
                        // Calculate department positions relative to entitas center
                        $canvasWidth = 2000;
                        $entitasCenterX = $canvasWidth / 2; // 1000px
                        $totalDeptWidth = ($totalDepts - 1) * $deptSpacing + 200; // Total width of all departments
                        $deptStartX = $entitasCenterX - $totalDeptWidth / 2; // Start position to center departments

                        $xPosition = $deptStartX + $deptIndex * $deptSpacing;
                        $divisiGroups = $dept->karyawan->groupBy('id_divisi');
                        $currentDivisiCount = $divisiGroups->count();
                    @endphp

                    <!-- Department Node -->
                    <div class="org-node departemen" data-type="departemen" data-id="{{ $dept->id }}"
                        style="top: {{ $yOffset }}px; left: {{ $xPosition }}px;">
                        <div class="text-xl mb-2">📁</div>
                        <div class="font-bold">{{ $dept->nama_departemen }}</div>
                        <div class="node-details">
                            {{ $dept->karyawan->count() }} Karyawan
                        </div>
                        <div class="employee-count">
                            {{ $divisiGroups->count() }} Divisi
                        </div>
                    </div>



                    @php
                        $divIndex = 0;
                        $divYOffset = $yOffset + 250; // Increased from 200 to 250 for better spacing

                        // Calculate spacing based on max jabatan per divisi (same algorithm as departments)
                        $maxJabatanCount = 0;
                        foreach ($divisiGroups as $divisiId => $karyawanInDivisi) {
                            $jabatanCount = $karyawanInDivisi->groupBy('id_jabatan')->count();
                            if ($jabatanCount > $maxJabatanCount) {
                                $maxJabatanCount = $jabatanCount;
                            }
                        }

                        // Calculate dynamic spacing: boxes are 200px wide, need minimum 280px spacing
                        $baseDivisiSpacing = 280; // Increased from 200 to 280 to prevent overlap
                        $extraDivisiSpacing = max(0, ($maxJabatanCount - 2) * 80); // Extra 80px per jabatan beyond 2
                        $divisiSpacing = $baseDivisiSpacing + $extraDivisiSpacing;

                        // Calculate centered positioning for divisions with dynamic spacing
                        $totalDivisiWidth = ($currentDivisiCount - 1) * $divisiSpacing;
                        $startDivisiX = $xPosition + 100 - $totalDivisiWidth / 2; // Center divisions under department
                    @endphp

                    @foreach ($divisiGroups as $divisiId => $karyawanInDivisi)
                        @php
                            $divisi = $karyawanInDivisi->first()->divisi;
                            $divXPosition = $startDivisiX + $divIndex * $divisiSpacing; // Use dynamic spacing
                            $jabatanGroups = $karyawanInDivisi->groupBy('id_jabatan');
                        @endphp

                        <!-- Division Node -->
                        <div class="org-node divisi" data-type="divisi" data-id="{{ $divisiId }}"
                            style="top: {{ $divYOffset }}px; left: {{ $divXPosition }}px;">
                            <div class="text-lg mb-2">📂</div>
                            <div class="font-bold">
                                {{ $divisi ? $divisi->nama_divisi : 'Divisi Tidak Ditentukan' }}
                            </div>
                            <div class="node-details">
                                {{ $karyawanInDivisi->count() }} Karyawan
                            </div>
                            <div class="employee-count">
                                {{ $jabatanGroups->count() }} Jabatan
                            </div>
                        </div>



                        @php
                            $jabIndex = 0;
                            $jabYOffset = $divYOffset + 220; // Starting Y position for first jabatan

                            // CHANGED: Jabatan akan disusun vertikal (ke bawah) bukan horizontal
                            // Vertical spacing between jabatan boxes
                            $jabatanVerticalSpacing = 150; // 150px spacing between jabatan boxes vertically
                        @endphp

                        @foreach ($jabatanGroups as $jabatanId => $karyawanInJabatan)
                            @foreach ($karyawanInJabatan as $karyawan)
                                @php
                                    // CHANGED: Show individual karyawan instead of jabatan groups
                                    $jabXPosition = $divXPosition; // Same X as parent divisi (centered)
                                    $jabYPosition = $jabYOffset + $jabIndex * $jabatanVerticalSpacing; // Vertical stacking
                                @endphp

                                <!-- Karyawan Node (instead of Jabatan) -->
                                <div class="org-node karyawan" data-type="karyawan" data-id="{{ $karyawan->id }}"
                                    style="top: {{ $jabYPosition }}px; left: {{ $jabXPosition }}px;">
                                    <div class="text-lg mb-2">👤</div>
                                    <div class="font-bold">{{ $karyawan->nama_karyawan }}</div>
                                    <div class="node-details">
                                        {{ $karyawan->jabatan->nama_jabatan ?? 'Jabatan Tidak Ditentukan' }}</div>
                                </div>

                                @php $jabIndex++; @endphp
                            @endforeach
                        @endforeach

                        @php $divIndex++; @endphp
                    @endforeach

                    @php $deptIndex++; @endphp
                @endforeach

                <!-- Connection lines container for JavaScript rendering -->
                <div id="connection-lines-container"></div>





            </div>
        </div>
    </div>

    <script>
        let scale = 1;
        let panX = 0;
        let panY = 0;
        let isDragging = false;
        let lastMouseX = 0;
        let lastMouseY = 0;

        // Function to draw connection lines based on actual box positions
        function drawConnectionLines() {
            const container = document.getElementById('connection-lines-container');
            container.innerHTML = ''; // Clear existing lines

            const entitasNode = document.querySelector('.org-node.entitas');
            const departemenNodes = document.querySelectorAll('.org-node.departemen');
            const divisiNodes = document.querySelectorAll('.org-node.divisi');
            const jabatanNodes = document.querySelectorAll('.org-node.jabatan');

            if (!entitasNode || departemenNodes.length === 0) return;

            // Get actual positions and dimensions
            function getNodeCenter(node) {
                const rect = node.getBoundingClientRect();
                const canvasRect = document.querySelector('.canvas-content').getBoundingClientRect();

                return {
                    x: (rect.left - canvasRect.left) / scale + rect.width / 2 / scale,
                    y: (rect.top - canvasRect.top) / scale + rect.height / 2 / scale,
                    bottom: (rect.top - canvasRect.top) / scale + rect.height / scale,
                    top: (rect.top - canvasRect.top) / scale
                };
            }

            function createLine(x1, y1, x2, y2, className = 'connection-line') {
                const line = document.createElement('div');
                line.className = className;

                const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
                const angle = Math.atan2(y2 - y1, x2 - x1) * 180 / Math.PI;

                line.style.position = 'absolute';
                line.style.left = x1 + 'px';
                line.style.top = y1 + 'px';
                line.style.width = length + 'px';
                line.style.height = '2px';
                line.style.background = '#6b7280';
                line.style.transformOrigin = '0 50%';
                line.style.transform = `rotate(${angle}deg)`;
                line.style.zIndex = '1';

                return line;
            }

            function createVerticalLine(x, y, height) {
                const line = document.createElement('div');
                line.className = 'vertical-line';
                line.style.position = 'absolute';
                line.style.left = x + 'px';
                line.style.top = y + 'px';
                line.style.width = '2px';
                line.style.height = height + 'px';
                line.style.background = '#6b7280';
                line.style.zIndex = '1';
                return line;
            }

            function createHorizontalLine(x, y, width) {
                const line = document.createElement('div');
                line.className = 'horizontal-line';
                line.style.position = 'absolute';
                line.style.left = x + 'px';
                line.style.top = y + 'px';
                line.style.width = width + 'px';
                line.style.height = '2px';
                line.style.background = '#6b7280';
                line.style.zIndex = '1';
                return line;
            }

            // Draw Entitas to Departments connections
            const entitasCenter = getNodeCenter(entitasNode);

            if (departemenNodes.length === 1) {
                // Single department - direct line
                const deptCenter = getNodeCenter(departemenNodes[0]);
                const verticalLine = createVerticalLine(entitasCenter.x - 1, entitasCenter.bottom, deptCenter.top -
                    entitasCenter.bottom);
                container.appendChild(verticalLine);
            } else {
                // Multiple departments - T-junction pattern
                const deptCenters = Array.from(departemenNodes).map(getNodeCenter);
                const firstDept = deptCenters[0];
                const lastDept = deptCenters[deptCenters.length - 1];

                // Horizontal line Y position (midway between entitas bottom and dept tops)
                const horizontalY = entitasCenter.bottom + (firstDept.top - entitasCenter.bottom) / 2;

                // Main vertical line from entitas
                const mainVertical = createVerticalLine(entitasCenter.x - 1, entitasCenter.bottom, horizontalY -
                    entitasCenter.bottom);
                container.appendChild(mainVertical);

                // Horizontal line connecting all departments
                const horizontalLine = createHorizontalLine(firstDept.x - 1, horizontalY - 1, lastDept.x - firstDept.x + 2);
                container.appendChild(horizontalLine);

                // Vertical drop lines to each department
                deptCenters.forEach(deptCenter => {
                    const dropLine = createVerticalLine(deptCenter.x - 1, horizontalY, deptCenter.top -
                        horizontalY);
                    container.appendChild(dropLine);
                });
            }

            // Draw Department to Division connections
            departemenNodes.forEach(deptNode => {
                const deptCenter = getNodeCenter(deptNode);
                const deptId = deptNode.dataset.id;
                const relatedDivisions = Array.from(divisiNodes).filter(div => {
                    // Find divisions that belong to this department
                    // Use flexible proximity detection for dynamic spacing
                    const divRect = div.getBoundingClientRect();
                    const deptRect = deptNode.getBoundingClientRect();
                    return Math.abs(divRect.left - deptRect.left) <
                        600; // Increased to handle dynamic spacing
                });

                if (relatedDivisions.length === 0) return;

                if (relatedDivisions.length === 1) {
                    // Single division - direct line
                    const divCenter = getNodeCenter(relatedDivisions[0]);
                    const verticalLine = createVerticalLine(deptCenter.x - 1, deptCenter.bottom, divCenter.top -
                        deptCenter.bottom);
                    container.appendChild(verticalLine);
                } else {
                    // Multiple divisions - T-junction pattern
                    const divCenters = relatedDivisions.map(getNodeCenter);
                    const firstDiv = divCenters[0];
                    const lastDiv = divCenters[divCenters.length - 1];

                    const horizontalY = deptCenter.bottom + (firstDiv.top - deptCenter.bottom) / 2;

                    // Main vertical from department
                    const mainVertical = createVerticalLine(deptCenter.x - 1, deptCenter.bottom, horizontalY -
                        deptCenter.bottom);
                    container.appendChild(mainVertical);

                    // Horizontal line
                    const horizontalLine = createHorizontalLine(firstDiv.x - 1, horizontalY - 1, lastDiv.x -
                        firstDiv.x + 2);
                    container.appendChild(horizontalLine);

                    // Drop lines
                    divCenters.forEach(divCenter => {
                        const dropLine = createVerticalLine(divCenter.x - 1, horizontalY, divCenter.top -
                            horizontalY);
                        container.appendChild(dropLine);
                    });
                }
            });

            // Draw Division to Karyawan connections (vertical layout)
            divisiNodes.forEach(divNode => {
                const divCenter = getNodeCenter(divNode);
                const karyawanNodes = document.querySelectorAll('.org-node.karyawan');

                const relatedKaryawan = Array.from(karyawanNodes).filter(kar => {
                    const karRect = kar.getBoundingClientRect();
                    const divRect = divNode.getBoundingClientRect();
                    // Check if karyawan is vertically aligned under this divisi (same X position)
                    return Math.abs(karRect.left - divRect.left) < 50;
                });

                if (relatedKaryawan.length === 0) return;

                // For vertical layout: draw direct lines from divisi to each karyawan
                relatedKaryawan.forEach(karNode => {
                    const karCenter = getNodeCenter(karNode);
                    // Direct vertical line from divisi to karyawan
                    const verticalLine = createVerticalLine(divCenter.x - 1, divCenter.bottom, karCenter
                        .top - divCenter.bottom);
                    container.appendChild(verticalLine);
                });
            });
        }

        const container = document.getElementById('canvasContainer');
        const content = document.getElementById('canvasContent');
        const zoomInfo = document.getElementById('zoomInfo');

        // Mouse events for panning
        container.addEventListener('mousedown', startDrag);
        container.addEventListener('mousemove', drag);
        container.addEventListener('mouseup', endDrag);
        container.addEventListener('mouseleave', endDrag);

        // Wheel event for zooming
        container.addEventListener('wheel', zoom);

        function startDrag(e) {
            if (e.target.classList.contains('org-node')) return;
            isDragging = true;
            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
            container.style.cursor = 'grabbing';
        }

        function drag(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - lastMouseX;
            const deltaY = e.clientY - lastMouseY;

            panX += deltaX;
            panY += deltaY;

            updateTransform();
            updateZoomInfo();

            lastMouseX = e.clientX;
            lastMouseY = e.clientY;
        }

        function endDrag() {
            isDragging = false;
            container.style.cursor = 'grab';
        }

        function zoom(e) {
            e.preventDefault();

            const rect = container.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const delta = e.deltaY > 0 ? 0.9 : 1.1;
            const newScale = Math.max(0.1, Math.min(3, scale * delta));

            // Adjust pan to zoom towards mouse position
            panX = mouseX - (mouseX - panX) * (newScale / scale);
            panY = mouseY - (mouseY - panY) * (newScale / scale);

            scale = newScale;
            updateTransform();
            updateZoomInfo();
        }

        function updateTransform() {
            content.style.transform = `translate(${panX}px, ${panY}px) scale(${scale})`;
            // Redraw connection lines after transform
            setTimeout(drawConnectionLines, 50);
        }

        function updateZoomInfo() {
            zoomInfo.textContent = `Zoom: ${Math.round(scale * 100)}% | Pan: ${Math.round(panX)}, ${Math.round(panY)}`;
        }

        function resetView() {
            scale = 1;
            panX = 0;
            panY = 0;
            updateTransform();
            updateZoomInfo();
        }

        function zoomIn() {
            scale = Math.min(3, scale * 1.2);
            updateTransform();
            updateZoomInfo();
        }

        function zoomOut() {
            scale = Math.max(0.1, scale * 0.8);
            updateTransform();
            updateZoomInfo();
        }

        function fitToScreen() {
            const containerRect = container.getBoundingClientRect();
            const contentRect = content.getBoundingClientRect();

            const scaleX = containerRect.width / 2000;
            const scaleY = containerRect.height / 1500;
            scale = Math.min(scaleX, scaleY, 1);

            panX = (containerRect.width - 2000 * scale) / 2;
            panY = (containerRect.height - 1500 * scale) / 2;

            updateTransform();
            updateZoomInfo();
        }

        // Tooltip functionality
        const tooltip = document.getElementById('nodeTooltip');

        function showTooltip(e, node) {
            const type = node.dataset.type;
            const rect = container.getBoundingClientRect();

            let tooltipContent = '';
            if (type === 'entitas') {
                tooltipContent = `<strong>🏢 ${node.querySelector('.font-bold').textContent}</strong><br>
                                 ${node.querySelector('.node-details').textContent}`;
            } else if (type === 'departemen') {
                tooltipContent = `<strong>📁 ${node.querySelector('.font-bold').textContent}</strong><br>
                                 ${node.querySelector('.node-details').textContent}<br>
                                 ${node.querySelector('.employee-count').textContent}`;
            } else if (type === 'divisi') {
                tooltipContent = `<strong>📂 ${node.querySelector('.font-bold').textContent}</strong><br>
                                 ${node.querySelector('.node-details').textContent}<br>
                                 ${node.querySelector('.employee-count').textContent}`;
            } else if (type === 'jabatan') {
                tooltipContent = `<strong>🏷️ ${node.querySelector('.font-bold').textContent}</strong><br>
                                 ${node.querySelector('.node-details').textContent}`;
            }

            tooltip.innerHTML = tooltipContent;
            tooltip.style.left = (e.clientX - rect.left + 10) + 'px';
            tooltip.style.top = (e.clientY - rect.top - 10) + 'px';
            tooltip.classList.add('show');
        }

        function hideTooltip() {
            tooltip.classList.remove('show');
        }

        // Node events
        document.querySelectorAll('.org-node').forEach(node => {
            node.addEventListener('mouseenter', function(e) {
                showTooltip(e, this);
            });

            node.addEventListener('mouseleave', hideTooltip);

            node.addEventListener('mousemove', function(e) {
                const rect = container.getBoundingClientRect();
                tooltip.style.left = (e.clientX - rect.left + 10) + 'px';
                tooltip.style.top = (e.clientY - rect.top - 10) + 'px';
            });

            node.addEventListener('click', function(e) {
                e.stopPropagation();
                const type = this.dataset.type;
                const id = this.dataset.id;
                const name = this.querySelector('.font-bold').textContent;

                // Enhanced click handler with more details
                const details = this.querySelector('.node-details').textContent;
                const message =
                    `${type.toUpperCase()}: ${name}\n${details}\n\nKlik OK untuk melihat detail lebih lanjut`;

                if (confirm(message)) {
                    // You can add navigation or modal here
                    console.log(`Navigate to ${type} detail page for ID: ${id}`);
                }
            });
        });

        // Minimap functionality
        function updateMinimap() {
            const minimap = document.getElementById('minimap');
            const minimapViewport = document.getElementById('minimapViewport');

            if (!minimap || !minimapViewport) return;

            const containerRect = container.getBoundingClientRect();
            const contentRect = content.getBoundingClientRect();

            // Calculate viewport position in minimap
            const minimapScale = 0.1;
            const viewportX = (-panX * minimapScale);
            const viewportY = (-panY * minimapScale);
            const viewportWidth = (containerRect.width / scale) * minimapScale;
            const viewportHeight = (containerRect.height / scale) * minimapScale;

            minimapViewport.style.left = viewportX + 'px';
            minimapViewport.style.top = viewportY + 'px';
            minimapViewport.style.width = viewportWidth + 'px';
            minimapViewport.style.height = viewportHeight + 'px';
        }

        // Override updateTransform to include minimap update
        const originalUpdateTransform = updateTransform;
        updateTransform = function() {
            originalUpdateTransform();
            updateMinimap();
        };

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.target.tagName === 'INPUT') return; // Don't interfere with input fields

            switch (e.key) {
                case 'r':
                case 'R':
                    resetView();
                    break;
                case '+':
                case '=':
                    e.preventDefault();
                    zoomIn();
                    break;
                case '-':
                    e.preventDefault();
                    zoomOut();
                    break;
                case 'f':
                case 'F':
                    fitToScreen();
                    break;
            }
        });

        // Canvas search functionality
        const canvasSearch = document.getElementById('canvasSearch');
        const clearCanvasSearch = document.getElementById('clearCanvasSearch');

        canvasSearch.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();

            if (searchTerm === '') {
                clearCanvasSearchHighlight();
                clearCanvasSearch.classList.add('hidden');
                return;
            }

            clearCanvasSearch.classList.remove('hidden');
            clearCanvasSearch.classList.add('flex');

            // Search and highlight nodes
            const nodes = document.querySelectorAll('.org-node');
            let foundNode = null;

            nodes.forEach(node => {
                const text = node.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    node.style.border = '3px solid #fbbf24';
                    node.style.boxShadow = '0 0 20px rgba(251, 191, 36, 0.5)';
                    if (!foundNode) foundNode = node;
                } else {
                    node.style.border = '2px solid transparent';
                    node.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
                }
            });

            // Pan to first found node
            if (foundNode) {
                const nodeRect = foundNode.getBoundingClientRect();
                const containerRect = container.getBoundingClientRect();

                panX = containerRect.width / 2 - (parseInt(foundNode.style.left) + 100) * scale;
                panY = containerRect.height / 2 - (parseInt(foundNode.style.top) + 60) * scale;

                updateTransform();
                updateZoomInfo();
            }
        });

        clearCanvasSearch.addEventListener('click', function() {
            canvasSearch.value = '';
            clearCanvasSearchHighlight();
            this.classList.add('hidden');
            this.classList.remove('flex');
        });

        function clearCanvasSearchHighlight() {
            const nodes = document.querySelectorAll('.org-node');
            nodes.forEach(node => {
                node.style.border = '2px solid transparent';
                node.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
            });
        }

        // Initialize canvas
        function initializeCanvas() {
            updateZoomInfo();
            updateMinimap();

            // Hide loading state after a short delay
            setTimeout(() => {
                const loading = document.getElementById('canvasLoading');
                if (loading) {
                    loading.style.opacity = '0';
                    setTimeout(() => {
                        loading.style.display = 'none';
                        // Draw connection lines after loading is complete
                        drawConnectionLines();
                    }, 300);
                }
            }, 1000);
        }

        // Start initialization
        initializeCanvas();
    </script>
</x-filament-panels::page>
