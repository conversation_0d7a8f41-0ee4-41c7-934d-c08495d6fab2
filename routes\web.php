<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    // Redirect based on user role if authenticated
    // if (Auth::check()) {
    //     $user = Auth::user();
    //     switch ($user->role) {
    //         case 'admin':
    //         case 'supervisor':
    //             return redirect()->route('filament.admin.pages.dashboard');
    //         case 'karyawan':
    //             return redirect()->route('filament.karyawan.pages.dashboard');
    //         default:
    //             Auth::logout();
    //             return redirect()->route('filament.admin.auth.login');
    //     }
    // }

    // If not authenticated, redirect to admin login
    return redirect()->route('filament.admin.auth.login');
});

// Define login route for redirection
Route::get('/login', function () {
    // Redirect based on user role if authenticated
    // if (Auth::check()) {
    //     $user = Auth::user();
    //     switch ($user->hasAnySystemRole) {
    //         case 'admin':
    //         case 'supervisor':
    //             return redirect()->route('filament.admin.pages.dashboard');
    //         case 'karyawan':
    //             return redirect()->route('filament.karyawan.pages.dashboard');
    //         default:
    //             Auth::logout();
    //             return redirect()->route('filament.admin.auth.login');
    //     }
    // }

    // If not authenticated, redirect to admin login
    return redirect()->route('filament.admin.auth.login');
})->name('login');

// User-Karyawan linking routes
Route::post('/link-user-karyawan', [App\Http\Controllers\UserKaryawanController::class, 'linkUserToKaryawan'])
    ->middleware(['auth'])
    ->name('link-user-karyawan');

Route::post('/unlink-user-karyawan', [App\Http\Controllers\UserKaryawanController::class, 'unlinkUserFromKaryawan'])
    ->middleware(['auth'])
    ->name('unlink-user-karyawan');

// Geofencing validation route for karyawan panel
Route::post('/karyawan/validate-geofencing', [App\Http\Controllers\GeofencingController::class, 'validateAttendanceLocation'])
    ->middleware(['auth'])
    ->name('karyawan.validate-geofencing');

// Dokumen download route
Route::get('/dokumen/download/{dokumen}', function ($id) {
    $dokumen = \App\Models\Dokumen::findOrFail($id);

    // Check if user has permission to download this document
    if (auth()->user()->role === 'karyawan') {
        $karyawan = auth()->user()->karyawan;
        if (!$karyawan || $dokumen->karyawan_id !== $karyawan->id) {
            abort(403, 'Unauthorized');
        }
    }

    if (!$dokumen->file_path || !file_exists(storage_path('app/' . $dokumen->file_path))) {
        abort(404, 'File not found');
    }

    return response()->download(storage_path('app/' . $dokumen->file_path), $dokumen->nama_dokumen);
})->middleware(['auth'])->name('dokumen.download');

Route::get('/tes-asset', function () {
    return asset('images/viera-logo.png');
});

// Payroll slip route
Route::get('/payroll/slip/{payrollTransaction}', function ($id) {
    $payroll = \App\Models\PayrollTransaction::with(['karyawan', 'payrollPeriod', 'payrollDeductions'])->findOrFail($id);

    // Check permission
    if (auth()->user()->role === 'karyawan') {
        $karyawan = auth()->user()->karyawan;
        if (!$karyawan || $payroll->karyawan_id !== $karyawan->id) {
            abort(403, 'Unauthorized');
        }
    }

    return view('payroll.slip', compact('payroll'));
})->middleware(['auth'])->name('payroll.slip');

// Demo route for date range picker
Route::get('/examples/date-range-picker-demo', function () {
    return view('examples.date-range-picker-demo');
})->name('date-range-picker.demo');

// Test route for date range picker
Route::get('/test-date-picker', function () {
    return view('test-date-picker');
})->name('test-date-picker');

// Kanban board route with project parameter
// Route::get('/admin/papan-kanban/{project?}', function ($project = null) {
//     return redirect()->route('filament.admin.pages.kanban-board', ['project' => $project]);
// })->middleware(['auth'])->name('kanban-board');
