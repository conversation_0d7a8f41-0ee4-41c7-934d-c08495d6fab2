
<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <div class="space-y-6">
        
        <div class="p-6 border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl dark:border-gray-600">
            <div class="flex items-start justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">
                        Pengaturan Periode Kerja Perusahaan
                    </h2>
                    <p class="mt-2 text-sm text-gray-600 dark:text-gray-300">
                        Atur hari pertama kerja dan cut-off date untuk menentukan periode kerja perusahaan.
                        Pengaturan ini akan mempengaruhi semua dashboard, payroll, absensi, dan perhitungan pelanggaran.
                    </p>
                </div>
                <div class="text-right">
                    <?php
                        $workPeriodInfo = \App\Models\CompanySettings::getWorkPeriodInfo();
                    ?>
                    <div class="text-xs text-blue-600 dark:text-blue-400">
                        Periode Saat Ini
                    </div>
                    <div class="text-sm font-medium text-blue-800 dark:text-blue-300">
                        <?php echo e($workPeriodInfo['period_label']); ?>

                    </div>
                    <div class="mt-1 text-xs text-blue-500 dark:text-blue-500">
                        <?php echo e($workPeriodInfo['description']); ?>

                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-white border border-gray-200 shadow-sm dark:bg-gray-900 rounded-xl dark:border-gray-700">
            <form wire:submit="save">
                <div class="p-6">
                    <?php echo e($this->form); ?>

                </div>

                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                            Pastikan pengaturan sudah benar sebelum menyimpan
                        </div>
                        <div class="flex space-x-3">
                            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'gray','wire:click' => 'resetToDefault','type' => 'button']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'gray','wire:click' => 'resetToDefault','type' => 'button']); ?>
                                Reset ke Default
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>

                            <?php if (isset($component)) { $__componentOriginal6330f08526bbb3ce2a0da37da512a11f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament::components.button.index','data' => ['color' => 'primary','type' => 'submit']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('filament::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(Illuminate\View\AnonymousComponent::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['color' => 'primary','type' => 'submit']); ?>
                                Simpan Pengaturan
                             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $attributes = $__attributesOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__attributesOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f)): ?>
<?php $component = $__componentOriginal6330f08526bbb3ce2a0da37da512a11f; ?>
<?php unset($__componentOriginal6330f08526bbb3ce2a0da37da512a11f); ?>
<?php endif; ?>
                        </div>
                    </div>
                </div>
            </form>
        </div>

        
        <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
            
            <div class="p-6 bg-white border border-gray-200 shadow-sm dark:bg-gray-900 rounded-xl dark:border-gray-700">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    📅 Informasi Periode Saat Ini
                </h3>
                <?php
                    $currentInfo = \App\Models\CompanySettings::getWorkPeriodInfo();
                ?>
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Periode Aktif:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($currentInfo['period_label']); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Hari Pertama:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($currentInfo['start_date']); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600 dark:text-gray-400">Cut-off Date:</span>
                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?php echo e($currentInfo['end_date']); ?></span>
                    </div>
                </div>
            </div>

            
            <div class="p-6 bg-white border border-gray-200 shadow-sm dark:bg-gray-900 rounded-xl dark:border-gray-700">
                <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                    ⚡ Dampak Pengaturan
                </h3>
                <div class="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                    <div class="flex items-start space-x-2">
                        <span class="text-green-500">✓</span>
                        <span>Dashboard akan menampilkan data sesuai periode kerja</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-green-500">✓</span>
                        <span>Payroll dihitung berdasarkan periode kerja</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-green-500">✓</span>
                        <span>Absensi diakumulasi per periode kerja</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-green-500">✓</span>
                        <span>Pelanggaran dihitung per periode kerja</span>
                    </div>
                    <div class="flex items-start space-x-2">
                        <span class="text-green-500">✓</span>
                        <span>Filter dashboard otomatis mengikuti periode</span>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="p-6 bg-white border border-gray-200 shadow-sm dark:bg-gray-900 rounded-xl dark:border-gray-700">
            <h3 class="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
                📋 Contoh Pengaturan Periode Kerja
            </h3>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div class="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
                    <h4 class="mb-2 font-medium text-blue-900 dark:text-blue-300">Periode 21-20 (Default)</h4>
                    <p class="text-sm text-blue-700 dark:text-blue-400">
                        Periode dimulai tanggal 21 dan berakhir tanggal 20 bulan berikutnya.
                        Cocok untuk perusahaan dengan sistem payroll pertengahan bulan.
                    </p>
                </div>

                <div class="p-4 border border-green-200 rounded-lg bg-green-50 dark:bg-green-900/20 dark:border-green-800">
                    <h4 class="mb-2 font-medium text-green-900 dark:text-green-300">Periode 1-31 (Kalender)</h4>
                    <p class="text-sm text-green-700 dark:text-green-400">
                        Periode mengikuti bulan kalender dari tanggal 1 sampai akhir bulan.
                        Cocok untuk perusahaan dengan sistem payroll bulanan.
                    </p>
                </div>

                <div class="p-4 border border-purple-200 rounded-lg bg-purple-50 dark:bg-purple-900/20 dark:border-purple-800">
                    <h4 class="mb-2 font-medium text-purple-900 dark:text-purple-300">Periode 25-24 (Payroll)</h4>
                    <p class="text-sm text-purple-700 dark:text-purple-400">
                        Periode dimulai tanggal 25 dan berakhir tanggal 24 bulan berikutnya.
                        Cocok untuk sistem payroll tradisional.
                    </p>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php /**PATH D:\laragon\www\viera\resources\views/filament/pages/work-period-settings.blade.php ENDPATH**/ ?>